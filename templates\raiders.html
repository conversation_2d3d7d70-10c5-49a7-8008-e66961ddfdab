{% extends "base.html" %}

{% block title %}Raider Management - Uproar{% endblock %}

{% block content %}
<!-- <PERSON> Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-users text-accent me-2"></i>
                    Raider Management
                </h1>
                <p class="text-secondary mb-0">Manage guild recruitment and member status</p>
            </div>
            <div>
                <button id="updateDiscordIds" class="btn btn-outline-primary me-2">
                    <i class="fas fa-sync-alt me-2"></i>Update Discord IDs
                </button>
                <button id="updateButton" class="btn btn-primary">
                    <i class="fas fa-sync-alt me-2"></i>Refresh Data
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add New Raider Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    Add New Raider
                </h6>
            </div>
            <div class="card-body">
                <form id="addUrlForm" class="row g-3">
                    <div class="col-lg-5 col-md-6">
                        <label for="newUrl" class="form-label">
                            <i class="fas fa-link me-1"></i>Armory URL
                        </label>
                        <input type="url" class="form-control" id="newUrl"
                               placeholder="https://worldofwarcraft.blizzard.com/en-us/character/..." required>
                        <div class="form-text">Full character armory URL from Battle.net</div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <label for="discordId" class="form-label">
                            <i class="fas fa-hashtag me-1"></i>Discord ID
                        </label>
                        <input type="text" class="form-control" id="discordId"
                               placeholder="123456789012345678" required>
                        <div class="form-text">18-digit Discord user ID</div>
                    </div>
                    <div class="col-lg-2 col-md-6">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-success d-block w-100">
                            <i class="fas fa-plus me-2"></i>Add Raider
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
        <table class="rank-table">
            <thead>
                <tr>
                    <th class="sortable" data-sort="status">Status</th>
                    <th class="sortable" data-sort="armory">WoW Armory</th>
                    <th class="sortable" data-sort="discord">Name</th>
                    <th class="sortable" data-sort="role">Discord Role</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for url, discord, status, role in armory_urls %}
                <tr>
                    <td class="status-cell {{ status.lower() }}">
                        {{ status }}
                    </td>
                    <td>
                        <a href="{{ url }}" target="_blank">{{ url }}</a>
                    </td>
                    <td>
                        {{ discord if discord else 'Not provided' }}
                    </td>
                    <td>
                        <span class="role-status {{ role.lower().replace(' ', '-') }}">{{ role }}</span>
                    </td>
                    <td>
                        {% if status.lower() == 'approved' %}
                            <button class="btn btn-danger btn-sm remove-url action-btn" data-url="{{ url }}">Revoke</button>
                        {% elif status.lower() == 'inactive' %}
                            <button class="btn btn-success btn-sm reactivate-url action-btn" data-url="{{ url }}">Activate</button>
                        {% else %}
                            <button class="btn btn-warning btn-sm approve-url action-btn" data-url="{{ url }}">Approve</button>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Update button functionality
            const updateButton = document.getElementById('updateButton');
            if (updateButton) {
                updateButton.addEventListener('click', async function() {
                    try {
                        updateButton.disabled = true;
                        updateButton.textContent = 'Updating...';
                        
                        const response = await fetch('/update_data', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });
                        
                        const data = await response.json();
                        
                        if (response.ok) {
                            document.getElementById('lastUpdated').textContent = `Last updated: ${data.timestamp}`;
                            alert('Data update completed successfully');
                            location.reload();
                        } else {
                            throw new Error(data.error || 'Failed to update data');
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        alert(`Update failed: ${error.message}`);
                    } finally {
                        updateButton.disabled = false;
                        updateButton.textContent = 'Update Data';
                    }
                });
            }

            // Add URL form functionality
            const addUrlForm = document.getElementById('addUrlForm');
            if (addUrlForm) {
                addUrlForm.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    let newUrl = document.getElementById('newUrl').value;
                    const discordId = document.getElementById('discordId').value;
                    
                    // Remove trailing slash if present
                    newUrl = newUrl.replace(/\/$/, '');
                    
                    // Validate that the input is a valid Discord ID (numeric string)
                    if (!/^\d+$/.test(discordId)) {
                        alert('Invalid Discord ID. Please enter only numbers.');
                        return;
                    }
        
                    // After getting a valid Discord ID, ask for Discord name
                    const discordName = prompt('Nickname or raider name:');
                    if (!discordName) {
                        return;
                    }
                    
                    try {
                        const response = await fetch('/add_url', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ 
                                url: newUrl, 
                                discord: discordName,
                                discord_id: discordId 
                            })
                        });
                        
                        if (response.ok) {
                            location.reload();
                        } else {
                            const data = await response.json();
                            alert(data.error || 'Failed to add URL');
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        alert('Failed to add URL');
                    }
                });
            }

            // Approve URL buttons functionality
            document.querySelectorAll('.approve-url').forEach(button => {
                button.addEventListener('click', async function() {
                    const url = this.dataset.url;
                    if (confirm('Are you sure you want to approve this raider?')) {
                        try {
                            const response = await fetch('/approve_url', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({ url: url })
                            });
                            
                            const data = await response.json();
                            
                            if (response.ok) {
                                alert('Raider approved successfully');
                                // Only reload after showing the success message
                                location.reload();
                            } else {
                                // Show the detailed error message
                                const errorMessage = data.error || 'Failed to approve raider';
                                console.error('Error details:', data);
                                alert(`Error: ${errorMessage}`);
                            }
                        } catch (error) {
                            console.error('Error:', error);
                            alert(`Error: ${error.message}`);
                        }
                    }
                });
            });

            // Remove URL buttons functionality
            document.querySelectorAll('.remove-url').forEach(button => {
                button.addEventListener('click', async function() {
                    const url = this.dataset.url;
                    if (confirm('Are you sure you want to revoke this raider?')) {
                        try {
                            const response = await fetch('/remove_url', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({ url: url })
                            });
                            
                            if (response.ok) {
                                location.reload();
                            } else {
                                const data = await response.json();
                                alert(data.error || 'Failed to revoke raider');
                            }
                        } catch (error) {
                            console.error('Error:', error);
                            alert('Failed to revoke raider');
                        }
                    }
                });
            });

            // Reactivate URL buttons functionality
            document.querySelectorAll('.reactivate-url').forEach(button => {
                button.addEventListener('click', async function() {
                    const url = this.dataset.url;
                    if (confirm('Are you sure you want to reactivate this raider?')) {
                        try {
                            const response = await fetch('/reactivate_url', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({ url: url })
                            });
                            
                            if (response.ok) {
                                location.reload();
                            } else {
                                const data = await response.json();
                                alert(data.error || 'Failed to reactivate raider');
                            }
                        } catch (error) {
                            console.error('Error:', error);
                            alert('Failed to reactivate raider');
                        }
                    }
                });
            });

            // Fix table sorting functionality
            const table = document.querySelector('.rank-table');
            if (table) {
                const headers = table.querySelectorAll('th.sortable');
                let currentSort = { column: null, asc: true };

                headers.forEach(header => {
                    header.addEventListener('click', () => {
                        const column = header.dataset.sort;
                        const tbody = table.querySelector('tbody');
                        const rows = Array.from(tbody.querySelectorAll('tr'));
                        
                        if (currentSort.column === column) {
                            currentSort.asc = !currentSort.asc;
                        } else {
                            currentSort = { column: column, asc: true };
                        }
                        
                        headers.forEach(h => h.classList.remove('sort-asc', 'sort-desc'));
                        header.classList.add(currentSort.asc ? 'sort-asc' : 'sort-desc');

                        rows.sort((a, b) => {
                            let aVal, bVal;
                            
                            // Get the correct column based on the sort type
                            switch(column) {
                                case 'status':
                                    aVal = a.querySelector('.status-cell').textContent.trim();
                                    bVal = b.querySelector('.status-cell').textContent.trim();
                                    break;
                                case 'armory':
                                    aVal = a.querySelector('td:nth-child(2)').textContent.trim();
                                    bVal = b.querySelector('td:nth-child(2)').textContent.trim();
                                    break;
                                case 'discord':
                                    aVal = a.querySelector('td:nth-child(3)').textContent.trim();
                                    bVal = b.querySelector('td:nth-child(3)').textContent.trim();
                                    break;
                                case 'role':
                                    aVal = a.querySelector('td:nth-child(4)').textContent.trim();
                                    bVal = b.querySelector('td:nth-child(4)').textContent.trim();
                                    break;
                                default:
                                    aVal = '';
                                    bVal = '';
                            }
                            
                            return currentSort.asc ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
                        });

                        rows.forEach(row => tbody.appendChild(row));
                    });
                });
            }
        });
    </script>
    <script>
async function handleAddUrl() {
    const url = document.getElementById('url-input').value.trim();
    if (!url) {
        alert('Please enter a URL');
        return;
    }

    // First, ask for Discord ID
    const discord_id = prompt('Please enter the Discord ID:');
    if (!discord_id) {
        return;
    }

    // Validate that the input is a valid Discord ID (numeric string)
    if (!/^\d+$/.test(discord_id)) {
        alert('Invalid Discord ID. Please enter only numbers.');
        return;
    }

    // After getting a valid Discord ID, ask for Discord name
    const discord_name = prompt('Please enter the Discord name:');
    if (!discord_name) {
        return;
    }

    try {
        const response = await fetch('/add_url', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                url: url,
                discord: discord_name,
                discord_id: discord_id
            })
        });

        const data = await response.json();
        if (response.ok) {
            alert('URL added successfully');
            location.reload();
        } else {
            alert(`Error: ${data.error || 'Failed to add URL'}`);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error adding URL');
    }
}
</script>
    <style>
        .action-btn {
            width: 100px;
            text-align: center;
        }
        
        /* Add styling for role status */
        .role-status {
            font-weight: bold;
        }
        .role-status.assigned {
            color: #28a745;  /* Bootstrap success green */
        }
        .role-status.not-assigned {
            color: #dc3545;  /* Bootstrap danger red */
        }
    </style>
</body>
</html> 