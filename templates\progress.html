{% extends "base.html" %}

{% block title %}Progress - Uproar{% endblock %}

{% block content %}
<!-- <PERSON> Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-chart-line text-accent me-2"></i>
                    Mythic+ Progress
                </h1>
                <p class="text-secondary mb-0">Track weekly mythic+ dungeon progress</p>
            </div>
        </div>
    </div>
</div>

<!-- Week Selection -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-calendar-week me-2"></i>
                    Week Selection
                </h6>
            </div>
            <div class="card-body">
                <div class="week-overview d-flex justify-content-center align-items-end gap-2 py-3">
                    {% for week in weeks|sort %}
                    <a href="/progress?week={{ week }}" class="week-bar {% if week|string == selected_week|string %}active{% endif %}" data-week="{{ week }}" title="Week {{ week }}">
                        <div class="bar-container">
                            <div class="bar"></div>
                        </div>
                        <span class="week-number">{{ week }}</span>
                    </a>
                    {% endfor %}
                    <a href="/progress" class="week-bar {% if selected_week == 'current' %}active{% endif %}" data-week="current" title="Current Week">
                        <div class="bar-container">
                            <div class="bar"></div>
                        </div>
                        <span class="week-number">Current</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Progress Stats -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-body text-center">
                <div id="missing-vault-counter" class="progress-counter">
                    <div class="counter-value">0</div>
                    <div class="counter-label">M+ Vault Missing</div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card">
            <div class="card-body text-center">
                <div id="max-reward-counter" class="progress-counter">
                    <div class="counter-value">0</div>
                    <div class="counter-label">Max Reward Missing</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Progress Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Character Progress
                </h6>
                <div class="d-flex gap-2">
                    <input type="text" class="form-control form-control-sm search-input"
                           placeholder="Search characters..."
                           data-target=".table-modern"
                           style="width: 200px;">
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-modern mb-0">
                        <thead>
                            <tr>
                                <th data-sort="character-name">
                                    <i class="fas fa-user me-1"></i>Raider
                                </th>
                                <th data-sort="week-number">
                                    <i class="fas fa-calendar me-1"></i>Week N°
                                </th>
                                <th data-sort="total">
                                    <i class="fas fa-list-ol me-1"></i>Total
                                </th>
                                <th data-sort="this-week">
                                    <i class="fas fa-calendar-week me-1"></i>This Week
                                </th>
                                <th data-sort="highest">
                                    <i class="fas fa-trophy me-1"></i>Highest
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for character in characters %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="fw-bold character-name">{{ character.name }}</span>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-tertiary border-custom">{{ character.week_number }}</span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-info">{{ character.total_runs }}</span>
                                </td>
                                <td class="text-center">
                                    {% if character.this_week == 0 %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times me-1"></i>{{ character.this_week }}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>{{ character.this_week }}
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    {% if character.highest < 10 %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-arrow-down me-1"></i>{{ character.highest }}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-arrow-up me-1"></i>{{ character.highest }}
                                        </span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set the current selection based on URL parameter
    const urlParams = new URLSearchParams(window.location.search);
    const weekParam = urlParams.get('week');

    // Highlight the active week bar
    if (weekParam) {
        const activeBar = document.querySelector(`.week-bar[data-week="${weekParam}"]`);
        if (activeBar) {
            activeBar.classList.add('active');
        }
    } else {
        // If no week parameter, highlight "current"
        const currentWeekBar = document.querySelector('.week-bar[data-week="current"]');
        if (currentWeekBar) {
            currentWeekBar.classList.add('active');
        }
    }

    // Calculate and update counters
    function updateCounters() {
        const rows = document.querySelectorAll('.table-modern tbody tr:not([style*="display: none"])');
        let missingVaultCount = 0;
        let maxRewardMissingCount = 0;

        rows.forEach(row => {
            // Check "This Week" column (index 3) - look for badge with danger class
            const thisWeekBadge = row.children[3].querySelector('.badge');
            if (thisWeekBadge && thisWeekBadge.classList.contains('bg-danger')) {
                missingVaultCount++;
            }

            // Check "Highest" column (index 4) - look for badge with danger class
            const highestBadge = row.children[4].querySelector('.badge');
            if (highestBadge && highestBadge.classList.contains('bg-danger')) {
                maxRewardMissingCount++;
            }
        });

        // Update counter values
        const missingVaultCounter = document.querySelector('#missing-vault-counter .counter-value');
        missingVaultCounter.textContent = missingVaultCount;

        // Update styling based on count
        const missingVaultCounterDiv = document.getElementById('missing-vault-counter');
        if (missingVaultCount > 0) {
            missingVaultCounterDiv.classList.add('danger-counter');
            missingVaultCounterDiv.classList.remove('success-counter');
        } else {
            missingVaultCounterDiv.classList.add('success-counter');
            missingVaultCounterDiv.classList.remove('danger-counter');
        }

        // Update max reward missing counter
        const maxRewardCounter = document.querySelector('#max-reward-counter .counter-value');
        maxRewardCounter.textContent = maxRewardMissingCount;

        // Update styling based on count
        const maxRewardCounterDiv = document.getElementById('max-reward-counter');
        if (maxRewardMissingCount > 0) {
            maxRewardCounterDiv.classList.add('danger-counter');
            maxRewardCounterDiv.classList.remove('success-counter');
        } else {
            maxRewardCounterDiv.classList.add('success-counter');
            maxRewardCounterDiv.classList.remove('danger-counter');
        }
    }

    // Run counter update on page load
    updateCounters();

    // Add search functionality
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll('.table-modern tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });

            // Update counters after filtering
            updateCounters();
        });
    }
});
</script>

<!-- Additional CSS for progress-specific styling -->
<style>
/* Week overview styles */
.week-overview {
    height: 60px;
}

.week-bar {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.2s ease;
}

.week-bar:hover {
    transform: translateY(-2px);
    color: var(--text-primary);
}

.bar-container {
    height: 40px;
    display: flex;
    align-items: flex-end;
}

.bar {
    width: 12px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 3px 3px 0 0;
    transition: all 0.2s ease;
    height: 20px;
}

.week-bar:hover .bar {
    background-color: rgba(255, 255, 255, 0.7);
    height: 35px;
}

.week-bar.active .bar {
    background-color: var(--accent-red);
    height: 40px;
}

.week-number {
    font-size: 0.8rem;
    margin-top: 4px;
}

/* Progress counter styles */
.progress-counter {
    padding: 1rem;
    text-align: center;
}

.progress-counter .counter-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    display: block;
    margin-bottom: 0.5rem;
}

.progress-counter .counter-label {
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.progress-counter.danger-counter .counter-value {
    color: var(--danger-color);
}

.progress-counter.success-counter .counter-value {
    color: var(--success-color);
}
</style>
{% endblock %}














