<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        /* Custom styles for progress page counters */
        #missing-vault-counter, #max-reward-counter {
            min-width: 200px;
            white-space: nowrap;
            padding: 0.5em 1em;
        }
        
        /* Week overview styles */
        .week-overview {
            display: flex;
            align-items: flex-end;
            gap: 8px;
            height: 60px;
            margin: 0 20px;
        }
        
        .week-bar {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #fff;
            transition: all 0.2s ease;
        }
        
        .week-bar:hover {
            transform: translateY(-2px);
        }
        
        .bar-container {
            height: 40px;
            display: flex;
            align-items: flex-end;
        }
        
        .bar {
            width: 12px;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 3px 3px 0 0;
            transition: all 0.2s ease;
            height: 20px;
        }
        
        .week-bar:hover .bar {
            background-color: rgba(255, 255, 255, 0.7);
            height: 35px;
        }
        
        .week-bar.active .bar {
            background-color: #c41f3b;
            height: 40px;
        }
        
        .week-number {
            font-size: 0.8rem;
            margin-top: 4px;
        }
    </style>
</head>
<body>
    <header>
        <nav class="nav-container">
            <div class="nav-menu">
                {% include 'nav.html' %}
                <div class="nav-info">
                    <span id="lastUpdated" class="text-white">Last updated: {{ last_updated }}</span>
                </div>
            </div>
        </nav>
    </header>
    
    <div class="table-container">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <!-- Week overview with vertical bars -->
            <div class="week-overview">
                {% for week in weeks|sort %}
                <a href="/progress?week={{ week }}" class="week-bar {% if week|string == selected_week|string %}active{% endif %}" data-week="{{ week }}" title="Week {{ week }}">
                    <div class="bar-container">
                        <div class="bar"></div>
                    </div>
                    <span class="week-number">{{ week }}</span>
                </a>
                {% endfor %}
                <a href="/progress" class="week-bar {% if selected_week == 'current' %}active{% endif %}" data-week="current" title="Current Week">
                    <div class="bar-container">
                        <div class="bar"></div>
                    </div>
                    <span class="week-number">Current</span>
                </a>
            </div>
            
            <div class="d-flex gap-3">
                <div class="counter-group">
                    <div id="missing-vault-counter" class="tier-counter">
                        <span class="counter-value">0</span> M+ Vault Missing
                    </div>
                </div>
                <div class="counter-group">
                    <div id="max-reward-counter" class="tier-counter">
                        <span class="counter-value">0</span> Max Reward Missing
                    </div>
                </div>
            </div>
        </div>
        
        <table class="rank-table">
            <thead class="table-dark">
                <tr>
                    <th class="sortable text-start" data-sort="character-name">Raider</th>
                    <th class="sortable text-center" data-sort="week-number">Week N°</th>
                    <th class="sortable text-center" data-sort="total">Total</th>
                    <th class="sortable text-center" data-sort="this-week">This Week</th>
                    <th class="sortable text-center" data-sort="highest">Highest</th>
                </tr>
            </thead>
            <tbody>
                {% for character in characters %}
                <tr>
                    <td class="text-start">
                        <div class="character-name">
                            {{ character.name }}
                        </div>
                    </td>
                    <td class="text-center">{{ character.week_number }}</td>
                    <td class="text-center">{{ character.total_runs }}</td>
                    <td class="text-center" {% if character.this_week == 0 %}style="color: red; font-weight: bold;"{% elif character.this_week > 0 %}style="color: green; font-weight: bold;"{% endif %}>{{ character.this_week }}</td>
                    <td class="text-center" {% if character.highest < 10 %}style="color: red; font-weight: bold;"{% elif character.highest >= 10 %}style="color: green; font-weight: bold;"{% endif %}>{{ character.highest }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Set the current selection based on URL parameter
            const urlParams = new URLSearchParams(window.location.search);
            const weekParam = urlParams.get('week');
            
            // Highlight the active week bar
            if (weekParam) {
                const activeBar = document.querySelector(`.week-bar[data-week="${weekParam}"]`);
                if (activeBar) {
                    activeBar.classList.add('active');
                }
            } else {
                // If no week parameter, highlight "current"
                const currentWeekBar = document.querySelector('.week-bar[data-week="current"]');
                if (currentWeekBar) {
                    currentWeekBar.classList.add('active');
                }
            }

            // Calculate and update counters
            function updateCounters() {
                const rows = document.querySelectorAll('.rank-table tbody tr');
                let missingVaultCount = 0;
                let maxRewardMissingCount = 0;

                rows.forEach(row => {
                    // Check "This Week" column (index 3)
                    const thisWeekValue = parseInt(row.children[3].textContent.trim()) || 0;
                    if (thisWeekValue === 0) {
                        missingVaultCount++;
                    }

                    // Check "Highest" column (index 4)
                    const highestValue = parseInt(row.children[4].textContent.trim()) || 0;
                    if (highestValue < 10) {
                        maxRewardMissingCount++;
                    }
                });

                // Update counter values
                const missingVaultCounter = document.querySelector('#missing-vault-counter .counter-value');
                missingVaultCounter.textContent = missingVaultCount;
                
                // Color the counter red if there are any missing vaults
                const missingVaultCounterDiv = document.getElementById('missing-vault-counter');
                if (missingVaultCount > 0) {
                    missingVaultCounterDiv.style.color = 'red';
                    missingVaultCounterDiv.style.fontWeight = 'bold';
                } else {
                    missingVaultCounterDiv.style.color = '';
                    missingVaultCounterDiv.style.fontWeight = '';
                }

                // Update max reward missing counter
                const maxRewardCounter = document.querySelector('#max-reward-counter .counter-value');
                maxRewardCounter.textContent = maxRewardMissingCount;
                
                // Color the counter red if there are any missing max rewards
                const maxRewardCounterDiv = document.getElementById('max-reward-counter');
                if (maxRewardMissingCount > 0) {
                    maxRewardCounterDiv.style.color = 'red';
                    maxRewardCounterDiv.style.fontWeight = 'bold';
                } else {
                    maxRewardCounterDiv.style.color = '';
                    maxRewardCounterDiv.style.fontWeight = '';
                }
            }

            // Run counter update on page load
            updateCounters();

            // Enable sorting
            document.querySelectorAll('.sortable').forEach(header => {
                header.addEventListener('click', function() {
                    const sortBy = this.getAttribute('data-sort');
                    const tbody = document.querySelector('.rank-table tbody');
                    const rows = Array.from(tbody.querySelectorAll('tr'));
                    
                    rows.sort((a, b) => {
                        let aValue, bValue;
                        
                        if (sortBy === 'character-name') {
                            aValue = a.querySelector('.character-name').textContent.trim();
                            bValue = b.querySelector('.character-name').textContent.trim();
                            return aValue.localeCompare(bValue);
                        } else {
                            const columnIndex = Array.from(this.parentNode.children).indexOf(this);
                            aValue = parseInt(a.children[columnIndex].textContent) || 0;
                            bValue = parseInt(b.children[columnIndex].textContent) || 0;
                            return bValue - aValue; // Descending order for numeric values
                        }
                    });
                    
                    // Clear and re-append rows
                    rows.forEach(row => tbody.appendChild(row));
                    
                    // Update counters after sorting
                    updateCounters();
                });
            });
        });
    </script>

</body>
</html>














