{% extends "base.html" %}

{% block title %}Progress - Uproar{% endblock %}

{% block content %}
<!-- <PERSON> Header -->
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-chart-line text-accent me-2"></i>
                    Mythic+ Progress
                </h1>
                <p class="text-secondary mb-0">Track weekly mythic+ dungeon progress</p>
            </div>
            <!-- Compact counters in header -->
            <div class="d-flex gap-3">
                <div id="missing-vault-counter" class="compact-counter">
                    <span class="counter-value">0</span>
                    <small>M+ Vault Missing</small>
                </div>
                <div id="max-reward-counter" class="compact-counter">
                    <span class="counter-value">0</span>
                    <small>Max Reward Missing</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Week Selection - Compact -->
<div class="row mb-3">
    <div class="col-12">
        <div class="week-selection-compact d-flex justify-content-center align-items-center gap-3 py-2">
            <span class="text-secondary me-2">Week:</span>
            <div class="week-overview d-flex align-items-end gap-1">
                {% for week in weeks|sort %}
                <a href="/progress?week={{ week }}" class="week-bar {% if week|string == selected_week|string %}active{% endif %}" data-week="{{ week }}" title="Week {{ week }}">
                    <div class="bar-container">
                        <div class="bar"></div>
                    </div>
                    <span class="week-number">{{ week }}</span>
                </a>
                {% endfor %}
                <a href="/progress" class="week-bar {% if selected_week == 'current' %}active{% endif %}" data-week="current" title="Current Week">
                    <div class="bar-container">
                        <div class="bar"></div>
                    </div>
                    <span class="week-number">Current</span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Progress Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Character Progress
                </h6>
                <div class="d-flex gap-2">
                    <input type="text" class="form-control form-control-sm search-input"
                           placeholder="Search characters..."
                           data-target=".table-modern"
                           style="width: 200px;">
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-modern mb-0">
                        <thead>
                            <tr>
                                <th data-sort="character-name">
                                    <i class="fas fa-user me-1"></i>Raider
                                </th>
                                <th data-sort="week-number">
                                    <i class="fas fa-calendar me-1"></i>Week N°
                                </th>
                                <th data-sort="total">
                                    <i class="fas fa-list-ol me-1"></i>Total
                                </th>
                                <th data-sort="this-week">
                                    <i class="fas fa-calendar-week me-1"></i>This Week
                                </th>
                                <th data-sort="highest">
                                    <i class="fas fa-trophy me-1"></i>Highest
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for character in characters %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="fw-bold character-name">{{ character.name }}</span>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-tertiary border-custom">{{ character.week_number }}</span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-info">{{ character.total_runs }}</span>
                                </td>
                                <td class="text-center">
                                    {% if character.this_week == 0 %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times me-1"></i>{{ character.this_week }}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>{{ character.this_week }}
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    {% if character.highest < 10 %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-arrow-down me-1"></i>{{ character.highest }}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-arrow-up me-1"></i>{{ character.highest }}
                                        </span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set the current selection based on URL parameter
    const urlParams = new URLSearchParams(window.location.search);
    const weekParam = urlParams.get('week');

    // Highlight the active week bar
    if (weekParam) {
        const activeBar = document.querySelector(`.week-bar[data-week="${weekParam}"]`);
        if (activeBar) {
            activeBar.classList.add('active');
        }
    } else {
        // If no week parameter, highlight "current"
        const currentWeekBar = document.querySelector('.week-bar[data-week="current"]');
        if (currentWeekBar) {
            currentWeekBar.classList.add('active');
        }
    }

    // Calculate and update counters
    function updateCounters() {
        const rows = document.querySelectorAll('.table-modern tbody tr:not([style*="display: none"])');
        let missingVaultCount = 0;
        let maxRewardMissingCount = 0;

        rows.forEach(row => {
            // Check "This Week" column (index 3) - look for badge with danger class
            const thisWeekBadge = row.children[3].querySelector('.badge');
            if (thisWeekBadge && thisWeekBadge.classList.contains('bg-danger')) {
                missingVaultCount++;
            }

            // Check "Highest" column (index 4) - look for badge with danger class
            const highestBadge = row.children[4].querySelector('.badge');
            if (highestBadge && highestBadge.classList.contains('bg-danger')) {
                maxRewardMissingCount++;
            }
        });

        // Update counter values
        const missingVaultCounter = document.querySelector('#missing-vault-counter .counter-value');
        missingVaultCounter.textContent = missingVaultCount;

        // Update styling based on count
        const missingVaultCounterDiv = document.getElementById('missing-vault-counter');
        if (missingVaultCount > 0) {
            missingVaultCounterDiv.classList.add('danger-counter');
            missingVaultCounterDiv.classList.remove('success-counter');
        } else {
            missingVaultCounterDiv.classList.add('success-counter');
            missingVaultCounterDiv.classList.remove('danger-counter');
        }

        // Update max reward missing counter
        const maxRewardCounter = document.querySelector('#max-reward-counter .counter-value');
        maxRewardCounter.textContent = maxRewardMissingCount;

        // Update styling based on count
        const maxRewardCounterDiv = document.getElementById('max-reward-counter');
        if (maxRewardMissingCount > 0) {
            maxRewardCounterDiv.classList.add('danger-counter');
            maxRewardCounterDiv.classList.remove('success-counter');
        } else {
            maxRewardCounterDiv.classList.add('success-counter');
            maxRewardCounterDiv.classList.remove('danger-counter');
        }
    }

    // Run counter update on page load
    updateCounters();

    // Add search functionality
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll('.table-modern tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });

            // Update counters after filtering
            updateCounters();
        });
    }
});
</script>

<!-- Additional CSS for progress-specific styling -->
<style>
/* Compact counters in header */
.compact-counter {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.5rem 1rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    border: 1px solid var(--border-color);
    min-width: 120px;
}

.compact-counter .counter-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.compact-counter small {
    font-size: 0.7rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.25rem;
}

.compact-counter.danger-counter .counter-value {
    color: var(--danger-color);
}

.compact-counter.success-counter .counter-value {
    color: var(--success-color);
}

/* Compact week selection */
.week-selection-compact {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

/* Week overview styles - smaller */
.week-overview {
    height: 35px;
}

.week-bar {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.2s ease;
    margin: 0 1px;
}

.week-bar:hover {
    transform: translateY(-1px);
    color: var(--text-primary);
}

.bar-container {
    height: 20px;
    display: flex;
    align-items: flex-end;
}

.bar {
    width: 8px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 2px 2px 0 0;
    transition: all 0.2s ease;
    height: 12px;
}

.week-bar:hover .bar {
    background-color: rgba(255, 255, 255, 0.7);
    height: 18px;
}

.week-bar.active .bar {
    background-color: var(--accent-red);
    height: 20px;
}

.week-number {
    font-size: 0.7rem;
    margin-top: 2px;
    line-height: 1;
}

/* Make table more prominent */
.table-modern {
    font-size: 0.95rem;
}

.table-modern th {
    font-size: 0.85rem;
    padding: 0.75rem 0.5rem;
}

.table-modern td {
    padding: 0.75rem 0.5rem;
}
</style>
{% endblock %}














