<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Raid Logs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="icon" type="image/ico" href="{{ url_for('static', filename='favico.ico') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .nav-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .filter-container {
            margin-bottom: 20px;
        }
        
        .zone-filter {
            max-width: 300px;
        }
        
        /* Sortable table headers */
        th.sortable {
            cursor: pointer;
            position: relative;
        }
        
        th.sortable:after {
            content: '⇕';
            position: absolute;
            right: 8px;
            color: #999;
        }
        
        th.sort-asc:after {
            content: '↑';
            color: #fff;
        }
        
        th.sort-desc:after {
            content: '↓';
            color: #fff;
        }

        /* Fetch logs button styling */
        .fetch-logs-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 150px;
        }

        #fetch-logs-btn {
            min-width: 120px;
            transition: all 0.3s ease;
        }

        #fetch-logs-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        #cooldown-timer {
            font-size: 0.85rem;
        }

        #fetch-status {
            font-size: 0.85rem;
        }
    </style>
</head>
<body>
    <nav class="nav-container">
        <div class="nav-menu">
            {% include 'nav.html' %}
            <div class="nav-info">
                <span id="lastUpdated" class="text-white">Last updated: {{ last_updated }}</span>
            </div>
        </div>
    </nav>
    
    <div class="table-container">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="counter-group">
                <div class="date-selector">
                    <form method="get" action="/logs" class="d-flex align-items-center">
                        <select id="zone-filter" name="zone" class="form-select me-2">
                            <option value="">All Zones</option>
                            {% for zone in zones %}
                            <option value="{{ zone }}" {% if selected_zone == zone %}selected{% endif %}>{{ zone }}</option>
                            {% endfor %}
                        </select>

                        <select id="kill-filter" name="kill" class="form-select me-2">
                            <option value="true" {% if selected_kill == 'true' %}selected{% endif %}>Kills</option>
                            <option value="false" {% if selected_kill == 'false' %}selected{% endif %}>Wipes</option>
                        </select>

                        <select id="duplicate-filter" name="show_duplicates" class="form-select me-2">
                            <option value="false" {% if not show_duplicates %}selected{% endif %}>Hide Duplicates</option>
                            <option value="true" {% if show_duplicates %}selected{% endif %}>Show All Kills</option>
                        </select>

                        <button type="submit" class="btn btn-primary btn-sm ms-2">Filter</button>
                        {% if selected_zone or selected_kill != 'true' or show_duplicates %}
                        <a href="/logs" class="btn btn-outline-secondary btn-sm ms-2">Reset</a>
                        {% endif %}
                    </form>
                </div>
            </div>

            <!-- Fetch Logs Button -->
            <div class="fetch-logs-container">
                <button id="fetch-logs-btn" class="btn btn-success" onclick="fetchLogs()"
                        title="Fetch latest logs from Warcraft Logs (2 minute cooldown)">
                    <i class="fas fa-download me-2"></i>Fetch Logs
                </button>
                <div id="fetch-status" class="mt-2 text-center" style="display: none;">
                    <small class="text-warning">Fetching logs...</small>
                </div>
                <div id="cooldown-timer" class="mt-2 text-center" style="display: none;">
                    <small class="text-warning">Cooldown: <span id="timer-seconds">120</span>s</small>
                </div>
                <div id="error-message" class="mt-2 text-center" style="display: none;">
                    <small class="text-danger"></small>
                </div>
            </div>
        </div>
        
        <table class="rank-table">
            <thead>
                <tr>
                    <th class="sortable" data-sort="date">Date</th>
                    <th class="sortable" data-sort="encounter">Encounter</th>
                    <th class="sortable" data-sort="duration">Duration</th>
                    <th class="sortable" data-sort="kill">Kill</th>
                    <th class="sortable" data-sort="owner">Owner</th>
                    <th class="sortable" data-sort="zone">Zone</th>
                    <th>Log Link</th>
                </tr>
            </thead>
            <tbody>
                {% for log in logs %}
                <tr>
                    <td>{{ log.date }}</td>
                    <td>{{ log.encounter }}</td>
                    <td class="text-center">{{ log.duration }}</td>
                    <td class="text-center">
                        {% if log.kill %}
                        <i class="fas fa-check text-success"></i>
                        {% else %}
                        <i class="fas fa-times text-danger"></i>
                        {% endif %}
                    </td>
                    <td>{{ log.owner }}</td>
                    <td>{{ log.zone }}</td>
                    <td>
                        <a href="{{ log.link }}" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-external-link-alt"></i> View Log
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const table = document.querySelector('.rank-table');
            const headers = table.querySelectorAll('th.sortable');
            let currentSort = { column: null, asc: true };
            
            headers.forEach(header => {
                header.addEventListener('click', () => {
                    const column = header.dataset.sort;
                    const tbody = table.querySelector('tbody');
                    const rows = Array.from(tbody.querySelectorAll('tr'));
                    
                    // Update sort direction
                    if (currentSort.column === column) {
                        currentSort.asc = !currentSort.asc;
                    } else {
                        currentSort = { column: column, asc: true };
                    }
                    
                    // Sort the rows
                    rows.sort((a, b) => {
                        let aValue, bValue;
                        
                        // Special handling for kill column (icons)
                        if (column === 'kill') {
                            aValue = a.querySelector('td:nth-child(4) .fa-check') !== null;
                            bValue = b.querySelector('td:nth-child(4) .fa-check') !== null;
                        } else {
                            const columnIndex = Array.from(header.parentNode.children).indexOf(header);
                            aValue = a.children[columnIndex].textContent.trim();
                            bValue = b.children[columnIndex].textContent.trim();
                            
                            // Try to convert to numbers if possible
                            if (!isNaN(aValue) && !isNaN(bValue)) {
                                aValue = parseFloat(aValue);
                                bValue = parseFloat(bValue);
                            }
                        }
                        
                        // Compare values
                        if (aValue === bValue) return 0;
                        
                        // Determine sort direction
                        const direction = currentSort.asc ? 1 : -1;
                        
                        // Return comparison result
                        return aValue > bValue ? direction : -direction;
                    });
                    
                    // Update the DOM
                    rows.forEach(row => tbody.appendChild(row));
                    
                    // Update sort indicators
                    headers.forEach(h => {
                        h.classList.remove('sort-asc', 'sort-desc');
                    });
                    
                    header.classList.add(currentSort.asc ? 'sort-asc' : 'sort-desc');
                });
            });
            
            // Auto-select zone filter on change
            document.getElementById('zone-filter').addEventListener('change', function() {
                this.form.submit();
            });

            // Auto-select kill filter on change
            document.getElementById('kill-filter').addEventListener('change', function() {
                this.form.submit();
            });

            // Auto-select duplicate filter on change
            document.getElementById('duplicate-filter').addEventListener('change', function() {
                this.form.submit();
            });

            // Check for existing cooldown on page load
            checkExistingCooldown();
        });

        // Fetch logs functionality with cooldown
        let cooldownActive = false;
        let cooldownTimer = null;

        // Check for existing cooldown on page load
        function checkExistingCooldown() {
            const cooldownEnd = localStorage.getItem('fetchLogsCooldownEnd');
            if (cooldownEnd) {
                const now = Date.now();
                const endTime = parseInt(cooldownEnd);

                if (now < endTime) {
                    // Cooldown is still active
                    const remainingSeconds = Math.ceil((endTime - now) / 1000);
                    startCooldownWithTime(remainingSeconds);
                } else {
                    // Cooldown has expired, clean up
                    localStorage.removeItem('fetchLogsCooldownEnd');
                }
            }
        }

        function fetchLogs() {
            if (cooldownActive) {
                return;
            }

            const fetchBtn = document.getElementById('fetch-logs-btn');
            const fetchStatus = document.getElementById('fetch-status');
            const cooldownDiv = document.getElementById('cooldown-timer');
            const timerSpan = document.getElementById('timer-seconds');
            const errorDiv = document.getElementById('error-message');

            // Hide any previous error messages
            errorDiv.style.display = 'none';

            // Disable button and show status
            fetchBtn.disabled = true;
            fetchBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Fetching...';
            fetchStatus.style.display = 'block';

            // Make the API call
            fetch('/fetch-logs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                fetchStatus.style.display = 'none';

                if (data.message && !data.error) {
                    // Show success message briefly
                    fetchBtn.innerHTML = '<i class="fas fa-check me-2"></i>Success! Refresh the page!.';
                    fetchBtn.classList.remove('btn-success');
                    fetchBtn.classList.add('btn-success');

                    // Start cooldown and store end time
                    const cooldownEndTime = Date.now() + (120 * 1000); // 2 minutes from now
                    localStorage.setItem('fetchLogsCooldownEnd', cooldownEndTime.toString());
                    startCooldown();
                } else {
                    // Show error
                    fetchBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Error';
                    fetchBtn.classList.remove('btn-success');
                    fetchBtn.classList.add('btn-danger');

                    // Show error message
                    errorDiv.querySelector('small').textContent = data.error || 'Unknown error occurred';
                    errorDiv.style.display = 'block';

                    // Reset button after 5 seconds
                    setTimeout(() => {
                        resetFetchButton();
                        errorDiv.style.display = 'none';
                    }, 5000);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                fetchStatus.style.display = 'none';
                fetchBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Error';
                fetchBtn.classList.remove('btn-success');
                fetchBtn.classList.add('btn-danger');

                // Show error message
                errorDiv.querySelector('small').textContent = 'Network error occurred';
                errorDiv.style.display = 'block';

                // Reset button after 5 seconds
                setTimeout(() => {
                    resetFetchButton();
                    errorDiv.style.display = 'none';
                }, 5000);
            });
        }

        function startCooldown() {
            startCooldownWithTime(120); // 2 minutes
        }

        function startCooldownWithTime(seconds) {
            cooldownActive = true;
            const cooldownDiv = document.getElementById('cooldown-timer');
            const timerSpan = document.getElementById('timer-seconds');

            cooldownDiv.style.display = 'block';

            cooldownTimer = setInterval(() => {
                seconds--;
                timerSpan.textContent = seconds;

                if (seconds <= 0) {
                    clearInterval(cooldownTimer);
                    cooldownActive = false;
                    cooldownDiv.style.display = 'none';
                    localStorage.removeItem('fetchLogsCooldownEnd');
                    resetFetchButton();
                }
            }, 1000);

            // Set initial display
            timerSpan.textContent = seconds;
        }

        function resetFetchButton() {
            const fetchBtn = document.getElementById('fetch-logs-btn');
            fetchBtn.disabled = false;
            fetchBtn.innerHTML = '<i class="fas fa-download me-2"></i>Fetch Logs';
            fetchBtn.classList.remove('btn-danger');
            fetchBtn.classList.add('btn-success');
        }
    </script>
</body>
</html>






