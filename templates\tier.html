<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tier Overview</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="icon" type="image/ico" href="{{ url_for('static', filename='favico.ico') }}">
    <style>
        .nav-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
/* Tier token counters */
.tier-counters {
    color: white;
    font-size: 1.1em;
}
.tier-counters .d-flex {
    flex-wrap: wrap;
    justify-content: center;
}
.tier-counter {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.3em;
    padding: 0.5em 1em;
    border-radius: 5px;
    background: rgba(0, 0, 0, 0.3);
    text-align: center;
    min-height: 38px;
}

.tier-counter .counter-value {
    font-weight: bold;
}

/* Set counter specific styles */
.set-counter {
    flex-direction: column;
    gap: 0.2em;
}

.set-counter .counter-value {
    font-size: 1.1em;
}

.set-counter .counter-label {
    font-size: 0.9em;
}

        .tier-piece {
            text-align: center;
            
        }
        .tier-piece.has-piece {
            color: #2ecc71;
        }
        .tier-piece.no-piece {
            color: #e74c3c;
        }
        .counter-group {
            display: flex;
            gap: 10px;
            align-items: center;
            width: 100%;
            justify-content: space-between;
        }
        .date-selector {
            flex: 1;
            padding: 0.1em 1em;
        }
        .date-selector .form-select {
            width: 100%;
            max-width: none;
        }
        .tier-counter {
            flex: 1;
        }
        /* Add styles for the table to handle fixed column widths */
        .rank-table {
            table-layout: flex;
        }
        
        .rank-table th[data-sort="head"],
        .rank-table th[data-sort="shoulders"],
        .rank-table th[data-sort="chest"],
        .rank-table th[data-sort="hands"],
        .rank-table th[data-sort="legs"] {
            color: white;
            text-align: center;
        }

        .rank-table th[data-sort="character-name"],
        .rank-table th[data-sort="token"] {
            color: white;
            text-align: left;
        }

        /* Set counter colors */
    
        .set-counter.no-set {
            color: #c41f3b;  /* Red */
        }
        .set-counter.one-set {
            color: #c41f3b;  /* Red */
        }
        .set-counter.two-set {
            color: #f1c40f;  /* Yellow */
        }
        .set-counter.three-set {
            color: #f1c40f;  /* Yellow */
        }
        .set-counter.four-set {
            color: #00ff96;  /* Green */
        }
    </style>
</head>
<body>
    <nav class="nav-container">
        <div class="nav-menu">
            {% include 'nav.html' %}
            
            <div class="nav-info">
                <span id="lastUpdated" class="text-white">Last updated: {{ last_updated }}</span>
            </div>
        </div>
    </nav>
    <div class="table-container">
        <div class="d-flex flex-column gap-2 mb-3">
            <div class="counter-group">
                <div class="date-selector">
                    <select id="dateSelect" class="form-select">
                        <option value="current" {% if selected_date == 'current' %}selected{% endif %}>Current</option>
                    </select>
                </div>
                <div class="tier-counter tier-mystic">
                    <span class="counter-value">0</span> Mystic
                </div>

                <div class="tier-counter tier-venerated">
                    <span class="counter-value">0</span> Venerated
                </div>
                <div class="tier-counter tier-zenith">
                    <span class="counter-value">0</span> Zenith
                </div>
                <div class="tier-counter tier-dreadful">
                    <span class="counter-value">0</span> Dreadful
                </div>
            </div>             
            <div class="counter-group">
                <div class="tier-counter set-counter no-set">
                    <div class="counter-value"><span id="no-set-count">0</span>/<span id="total-raiders">0</span></div>
                    <div class="counter-label">No Set</div>
                </div>
                <div class="tier-counter set-counter one-set">
                    <div class="counter-value"><span id="one-set-count">0</span>/<span id="total-raiders-1">0</span></div>
                    <div class="counter-label">1-Set</div>
                </div>
                <div class="tier-counter set-counter two-set">
                    <div class="counter-value"><span id="two-set-count">0</span>/<span id="total-raiders-2">0</span></div>
                    <div class="counter-label">2-Set</div>
                </div>
                <div class="tier-counter set-counter three-set">
                    <div class="counter-value"><span id="three-set-count">0</span>/<span id="total-raiders-3">0</span></div>
                    <div class="counter-label">3-Set</div>
                </div>
                <div class="tier-counter set-counter four-set">
                    <div class="counter-value"><span id="four-set-count">0</span>/<span id="total-raiders-4">0</span></div>
                    <div class="counter-label">4-Set</div>
                </div>
            </div>

        </div>
       
        <table class="rank-table">
            <thead>
                <tr>
                    <th class="sortable" data-sort="character-name">Character</th>
                    <th class="sortable" data-sort="token">Token</th>
                    <th class="sortable" data-sort="head">Head</th>
                    <th class="sortable" data-sort="shoulders">Shoulders</th>
                    <th class="sortable" data-sort="chest">Chest</th>
                    <th class="sortable" data-sort="hands">Hands</th>
                    <th class="sortable" data-sort="legs">Legs</th>
                </tr>
            </thead>
            <tbody>
                {% for character in characters %}
                <tr>
                    <td>
                        <div class="character-name">
                            {{ character.name }}
                        </div>
                    </td>
                    <td class="tier-token tier-token-{{ character.token.lower() }}">{{ character.token }}</td>
                    <td class="tier-piece {% if character.head == 1 %}has-piece{% else %}no-piece{% endif %}">
                        {% if character.head == 1 %}✓{% else %}✗{% endif %}
                    </td>
                    <td class="tier-piece {% if character.shoulders == 1 %}has-piece{% else %}no-piece{% endif %}">
                        {% if character.shoulders == 1 %}✓{% else %}✗{% endif %}
                    </td>
                    <td class="tier-piece {% if character.chest == 1 %}has-piece{% else %}no-piece{% endif %}">
                        {% if character.chest == 1 %}✓{% else %}✗{% endif %}
                    </td>
                    <td class="tier-piece {% if character.hands == 1 %}has-piece{% else %}no-piece{% endif %}">
                        {% if character.hands == 1 %}✓{% else %}✗{% endif %}
                    </td>
                    <td class="tier-piece {% if character.legs == 1 %}has-piece{% else %}no-piece{% endif %}">
                        {% if character.legs == 1 %}✓{% else %}✗{% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const table = document.querySelector('.rank-table');
            const headers = table.querySelectorAll('th.sortable');
            let currentSort = { column: null, asc: true };

            // Load historical dates
            fetch('/get_historical_dates')
                .then(response => response.json())
                .then(data => {
                    const dateSelect = document.getElementById('dateSelect');
                    data.dates.forEach(date => {
                        const option = document.createElement('option');
                        option.value = date;
                        // Format date for display (YYYY-MM-DD)
                        const displayDate = `${date.substring(0, 4)}-${date.substring(4, 6)}-${date.substring(6, 8)}`;
                        option.textContent = displayDate;
                        if (date === '{{ selected_date }}') {
                            option.selected = true;
                        }
                        dateSelect.appendChild(option);
                    });
                })
                .catch(error => console.error('Error loading historical dates:', error));

            // Handle date selection change
            document.getElementById('dateSelect').addEventListener('change', function() {
                const selectedDate = this.value;
                window.location.href = `/tier?date=${selectedDate}`;
            });

            function countSetPieces(row) {
                const pieces = [
                    row.querySelector('td:nth-child(3)'), // Head
                    row.querySelector('td:nth-child(4)'), // Shoulders
                    row.querySelector('td:nth-child(5)'), // Chest
                    row.querySelector('td:nth-child(6)'), // Hands
                    row.querySelector('td:nth-child(7)')  // Legs
                ];
                const count = pieces.filter(piece => piece && piece.classList.contains('has-piece')).length;
                console.log('Set pieces count:', count, 'for row:', row.querySelector('.character-name').textContent);
                return count;
            }

            function updateCounters() {
                const rows = document.querySelectorAll('.rank-table tbody tr');
                const totalRaiders = rows.length;
                console.log('Total raiders:', totalRaiders);
                
                const tierCounters = {
                    'Mystic': 0,
                    'Dreadful': 0,
                    'Venerated': 0,
                    'Zenith': 0
                };
                
                let noSetCount = 0;
                let oneSetCount = 0;
                let twoSetCount = 0;
                let threeSetCount = 0;
                let fourSetCount = 0;

                rows.forEach(row => {
                    // Count tier tokens
                    const tierCell = row.querySelector('.tier-token');
                    if (tierCell) {
                        const tierText = tierCell.textContent.trim();
                        console.log('Found tier token:', tierText, 'for character:', row.querySelector('.character-name').textContent);
                        if (tierCounters.hasOwnProperty(tierText)) {
                            tierCounters[tierText]++;
                        }
                    }

                    // Count set pieces
                    const setPieces = countSetPieces(row);
                    if (setPieces === 0) noSetCount++;
                    if (setPieces === 1) oneSetCount++;
                    if (setPieces === 2) twoSetCount++;
                    if (setPieces === 3) threeSetCount++;
                    if (setPieces === 4 || setPieces === 5) fourSetCount++;
                });

                console.log('Tier counters:', tierCounters);
                console.log('Set counts:', { noSetCount, oneSetCount, twoSetCount, threeSetCount, fourSetCount });

                // Update tier counters
                Object.entries(tierCounters).forEach(([tier, count]) => {
                    const counter = document.querySelector(`.tier-${tier.toLowerCase()} .counter-value`);
                    if (counter) {
                        counter.textContent = count;
                    }
                });

                // Update set counters with ratios
                document.getElementById('no-set-count').textContent = noSetCount;
                document.getElementById('total-raiders').textContent = totalRaiders;
                document.getElementById('one-set-count').textContent = oneSetCount;
                document.getElementById('total-raiders-1').textContent = totalRaiders;
                document.getElementById('two-set-count').textContent = twoSetCount;
                document.getElementById('total-raiders-2').textContent = totalRaiders;
                document.getElementById('three-set-count').textContent = threeSetCount;
                document.getElementById('total-raiders-3').textContent = totalRaiders;
                document.getElementById('four-set-count').textContent = fourSetCount;
                document.getElementById('total-raiders-4').textContent = totalRaiders;
            }

            // Update counters initially
            updateCounters();

            headers.forEach(header => {
                header.addEventListener('click', () => {
                    const column = header.dataset.sort;
                    const tbody = table.querySelector('tbody');
                    const rows = Array.from(tbody.querySelectorAll('tr'));
                    
                    // Update sort direction
                    if (currentSort.column === column) {
                        currentSort.asc = !currentSort.asc;
                    } else {
                        currentSort = { column: column, asc: true };
                    }
                    
                    // Remove existing arrows
                    headers.forEach(h => h.classList.remove('sort-asc', 'sort-desc'));
                    // Add new arrow
                    header.classList.add(currentSort.asc ? 'sort-asc' : 'sort-desc');

                    // Sort rows
                    rows.sort((a, b) => {
                        let aVal, bVal;
                        if (column === 'character-name') {
                            aVal = a.querySelector('.character-name').textContent.trim();
                            bVal = b.querySelector('.character-name').textContent.trim();
                        } else {
                            aVal = a.querySelector(`td:nth-child(${Array.from(headers).indexOf(header) + 1})`).textContent.trim();
                            bVal = b.querySelector(`td:nth-child(${Array.from(headers).indexOf(header) + 1})`).textContent.trim();
                        }
                        return currentSort.asc ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
                    });

                    // Reorder rows
                    rows.forEach(row => tbody.appendChild(row));
                    
                    // Update counters after the rows are reordered
                    updateCounters();
                });
            });
        });
    </script>
</body>
</html> 
