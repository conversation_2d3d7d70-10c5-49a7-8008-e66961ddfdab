<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="icon" type="image/ico" href="{{ url_for('static', filename='favico.ico') }}">
</head>
<body>
    <nav class="nav-container">
        <div class="nav-menu">
            {% include 'nav.html' %}    
        </div>
    </nav>
   
    <div class="container d-flex justify-content-center align-items-center" style="min-height: 80vh;">
        <div class="col-md-5 col-lg-4">
            <div class="card shadow border-0 rounded-4 bg-dark text-white">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <h3>Raiders Access</h3>
                        <p class="text-muted">Enter password to continue</p>
                    </div>
                    <form method="POST">
                        <div class="form-floating mb-4">
                            <input type="password" class="form-control bg-dark text-white border-secondary" id="password" name="password" placeholder="Password" required>
                            <label for="password" class="text-secondary">Password</label>
                        </div>
                        {% if error %}
                        <div class="alert alert-danger py-2">{{ error }}</div>
                        {% endif %}
                        <button type="submit" class="btn btn-primary w-100 py-2 rounded-3">Login</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 
