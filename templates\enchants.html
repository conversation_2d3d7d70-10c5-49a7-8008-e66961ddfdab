<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enchants Overview</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="icon" type="image/ico" href="{{ url_for('static', filename='favico.ico') }}">
    <style>
        .nav-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        /* Enchant counters */
        .enchant-counters {
            max-width: 1200px;  /* Match table width */
            margin: 0 auto;
            color: white;
            font-size: 1em;
        }
        
        /* Counter rows layout */
        .enchant-counters .counter-row {
            display: flex;
            justify-content: space-between;
            gap: 1rem;
            width: 100%;
            margin-bottom: 1rem;
        }
        
        /* Counter items shared styles */
        .enchant-counter {
            flex: 1;  /* Make all counters take equal space */
            text-align: center;
            padding: 0.5em 1em;
            border-radius: 5px;
            background: rgba(0, 0, 0, 0.3);
            min-width: 0;  /* Allow flex to control width */
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 40px;  /* Control the height */
            border: 2px solid transparent;  /* Default transparent border */
        }
        
        /* Border colors based on counter value */
        .enchant-counter.zero-count {
            border-color: #27ae60;  /* Green border for zero */
        }
        
        .enchant-counter.non-zero-count {
            border-color: #e74c3c;  /* Red border for non-zero */
        }
        
        /* Date selector styling to match counters */
        .date-selector {
            flex: 1;
            padding: 0.1em 1em;
        }
        .date-selector .form-select {
            width: 100%;
            max-width: none;
        }
        .missing-enchant {
            background-color: #c0392b;
        }
        .has-enchant {
            color: #27ae60;  /* Green for check mark */
        }
        .no-enchant {
            color: #e74c3c;  /* Red for cross mark */
        }
        .enchant-piece {
            text-align: center;
        }
        .has-enchant, .no-enchant {
            display: inline-block;
            width: 100%;
            text-align: center;
        }
        .rank-table th[data-sort="head"],
        .rank-table th[data-sort="back"],
        .rank-table th[data-sort="chest"],
        .rank-table th[data-sort="wrist"],
        .rank-table th[data-sort="legs"],
        .rank-table th[data-sort="feet"],
        .rank-table th[data-sort="finger-1"],
        .rank-table th[data-sort="finger-2"],
        .rank-table th[data-sort="main-hand"] {
            color: white;
            text-align: center;
        }
    </style>
</head>
<body>
    <nav class="nav-container">
        <div class="nav-menu">
            {% include 'nav.html' %}
        
        <div class="nav-info">
            <span id="lastUpdated" class="text-white">Last updated: {{ last_updated }}</span>
        </div>
        </div>
    </nav>
    <div class="table-container">
        <div class="enchant-counters mb-3">
            <div class="counter-row">
                <div class="date-selector">
                    <select id="dateSelect" class="form-select">
                        {% for date in dates %}
                        <option value="{{ date }}" {% if date == selected_date %}selected{% endif %}>{{ date }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="enchant-counter missing-head">
                    <span class="counter-value">0</span> Missing Head
                </div>
                <div class="enchant-counter missing-back">
                    <span class="counter-value">0</span> Missing Back
                </div>
                <div class="enchant-counter missing-chest">
                    <span class="counter-value">0</span> Missing Chest
                </div>
                <div class="enchant-counter missing-wrist">
                    <span class="counter-value">0</span> Missing Wrist
                </div>
            </div>
            <div class="counter-row">
                <div class="enchant-counter missing-legs">
                    <span class="counter-value">0</span> Missing Legs
                </div>
                <div class="enchant-counter missing-feet">
                    <span class="counter-value">0</span> Missing Feet
                </div>
                <div class="enchant-counter missing-finger-1">
                    <span class="counter-value">0</span> Missing Ring 1
                </div>
                <div class="enchant-counter missing-finger-2">
                    <span class="counter-value">0</span> Missing Ring 2
                </div>
                <div class="enchant-counter missing-main-hand">
                    <span class="counter-value">0</span> Missing Weapon
                </div>
            </div>
        </div>
       
        <table class="rank-table">
            <thead>
                <tr>
                    <th class="sortable" data-sort="character-name">Character</th>
                    <th class="sortable" data-sort="head">Head</th>
                    <th class="sortable" data-sort="back">Back</th>
                    <th class="sortable" data-sort="chest">Chest</th>
                    <th class="sortable" data-sort="wrist">Wrist</th>
                    <th class="sortable" data-sort="legs">Legs</th>
                    <th class="sortable" data-sort="feet">Feet</th>
                    <th class="sortable" data-sort="finger-1">Ring 1</th>
                    <th class="sortable" data-sort="finger-2">Ring 2</th>
                    <th class="sortable" data-sort="main-hand">Weapon</th>
                </tr>
            </thead>
            <tbody>
                {% for character in characters %}
                <tr>
                    <td>
                        <div class="character-name">
                            {{ character.name }}
                        </div>
                    </td>
                    <td class="enchant-piece">
                        <span class="{% if character.Head %}has-enchant{% else %}no-enchant{% endif %}">
                            {% if character.Head %}✓{% else %}✗{% endif %}
                        </span>
                    </td>
                    <td class="enchant-piece">
                        <span class="{% if character.Back %}has-enchant{% else %}no-enchant{% endif %}">
                            {% if character.Back %}✓{% else %}✗{% endif %}
                        </span>
                    </td>
                    <td class="enchant-piece">
                        <span class="{% if character.Chest %}has-enchant{% else %}no-enchant{% endif %}">
                            {% if character.Chest %}✓{% else %}✗{% endif %}
                        </span>
                    </td>
                    <td class="enchant-piece">
                        <span class="{% if character.wrist %}has-enchant{% else %}no-enchant{% endif %}">
                            {% if character.wrist %}✓{% else %}✗{% endif %}
                        </span>
                    </td>
                    <td class="enchant-piece">
                        <span class="{% if character.Legs %}has-enchant{% else %}no-enchant{% endif %}">
                            {% if character.Legs %}✓{% else %}✗{% endif %}
                        </span>
                    </td>
                    <td class="enchant-piece">
                        <span class="{% if character.feet %}has-enchant{% else %}no-enchant{% endif %}">
                            {% if character.feet %}✓{% else %}✗{% endif %}
                        </span>
                    </td>
                    <td class="enchant-piece">
                        <span class="{% if character.finger_1 %}has-enchant{% else %}no-enchant{% endif %}">
                            {% if character.finger_1 %}✓{% else %}✗{% endif %}
                        </span>
                    </td>
                    <td class="enchant-piece">
                        <span class="{% if character.finger_2 %}has-enchant{% else %}no-enchant{% endif %}">
                            {% if character.finger_2 %}✓{% else %}✗{% endif %}
                        </span>
                    </td>
                    <td class="enchant-piece">
                        <span class="{% if character.Main_hand %}has-enchant{% else %}no-enchant{% endif %}">
                            {% if character.Main_hand %}✓{% else %}✗{% endif %}
                        </span>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const table = document.querySelector('.rank-table');
            const headers = table.querySelectorAll('th.sortable');
            let currentSort = { column: null, asc: true };

            // Load historical dates
            fetch('/get_historical_enchant_dates')
                .then(response => response.json())
                .then(data => {
                    const dateSelect = document.getElementById('dateSelect');
                    data.dates.forEach(date => {
                        const option = document.createElement('option');
                        option.value = date;
                        // Format date for display (YYYY-MM-DD)
                        const displayDate = `${date.substring(0, 4)}-${date.substring(4, 6)}-${date.substring(6, 8)}`;
                        option.textContent = displayDate;
                        if (date === '{{ selected_date }}') {
                            option.selected = true;
                        }
                        dateSelect.appendChild(option);
                    });
                })
                .catch(error => console.error('Error loading historical dates:', error));

            // Handle date selection change
            document.getElementById('dateSelect').addEventListener('change', function() {
                const selectedDate = this.value;
                window.location.href = `/enchants?date=${selectedDate}`;
            });

            function updateCounters() {
                const rows = document.querySelectorAll('.rank-table tbody tr');
                const totalRaiders = rows.length;
                
                const enchantCounters = {
                    'head': 0,
                    'back': 0,
                    'chest': 0,
                    'wrist': 0,
                    'legs': 0,
                    'feet': 0,
                    'finger-1': 0,
                    'finger-2': 0,
                    'main-hand': 0
                };

                rows.forEach(row => {
                    // Check each enchant slot
                    if (row.querySelector('td:nth-child(2) .no-enchant')) enchantCounters['head']++;
                    if (row.querySelector('td:nth-child(3) .no-enchant')) enchantCounters['back']++;
                    if (row.querySelector('td:nth-child(4) .no-enchant')) enchantCounters['chest']++;
                    if (row.querySelector('td:nth-child(5) .no-enchant')) enchantCounters['wrist']++;
                    if (row.querySelector('td:nth-child(6) .no-enchant')) enchantCounters['legs']++;
                    if (row.querySelector('td:nth-child(7) .no-enchant')) enchantCounters['feet']++;
                    if (row.querySelector('td:nth-child(8) .no-enchant')) enchantCounters['finger-1']++;
                    if (row.querySelector('td:nth-child(9) .no-enchant')) enchantCounters['finger-2']++;
                    if (row.querySelector('td:nth-child(10) .no-enchant')) enchantCounters['main-hand']++;
                });

                // Update enchant counters
                Object.entries(enchantCounters).forEach(([slot, count]) => {
                    const counterElement = document.querySelector(`.missing-${slot}`);
                    if (counterElement) {
                        const counterValue = counterElement.querySelector('.counter-value');
                        if (counterValue) {
                            counterValue.textContent = count;
                        }
                        
                        // Update border color based on count
                        if (count === 0) {
                            counterElement.classList.add('zero-count');
                            counterElement.classList.remove('non-zero-count');
                        } else {
                            counterElement.classList.add('non-zero-count');
                            counterElement.classList.remove('zero-count');
                        }
                    }
                });
            }

            // Update counters initially
            updateCounters();

            headers.forEach(header => {
                header.addEventListener('click', () => {
                    const column = header.dataset.sort;
                    const tbody = table.querySelector('tbody');
                    const rows = Array.from(tbody.querySelectorAll('tr'));
                    
                    // Update sort direction
                    if (currentSort.column === column) {
                        currentSort.asc = !currentSort.asc;
                    } else {
                        currentSort = { column: column, asc: true };
                    }
                    
                    // Remove existing arrows
                    headers.forEach(h => h.classList.remove('sort-asc', 'sort-desc'));
                    // Add new arrow
                    header.classList.add(currentSort.asc ? 'sort-asc' : 'sort-desc');

                    // Sort rows
                    rows.sort((a, b) => {
                        let aVal, bVal;
                        if (column === 'character-name') {
                            aVal = a.querySelector('.character-name').textContent.trim();
                            bVal = b.querySelector('.character-name').textContent.trim();
                        } else {
                            const colIndex = Array.from(headers).indexOf(header) + 1;
                            aVal = a.querySelector(`td:nth-child(${colIndex})`).textContent.trim();
                            bVal = b.querySelector(`td:nth-child(${colIndex})`).textContent.trim();
                        }
                        return currentSort.asc ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
                    });
                    
                    // Reorder rows
                    rows.forEach(row => tbody.appendChild(row));
                });
            });
        });
    </script>
</body>
</html>
