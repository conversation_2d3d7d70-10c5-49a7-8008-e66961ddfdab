{% extends "base.html" %}

{% block title %}Enchants - Uproar{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-magic text-accent me-2"></i>
                    Enchants Overview
                </h1>
                <p class="text-secondary mb-0">Track enchantment coverage across the guild</p>
            </div>
            <div>
                <select id="dateSelect" class="form-select" style="min-width: 200px;">
                    <option value="current" {% if selected_date == 'current' %}selected{% endif %}>Current</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- Missing Enchants Overview -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Missing Enchants Summary
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-lg-2 col-md-3 col-6 mb-3">
                        <div class="enchant-counter missing-head">
                            <span class="counter-value">0</span>
                            <small class="d-block">Head</small>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-3 col-6 mb-3">
                        <div class="enchant-counter missing-back">
                            <span class="counter-value">0</span>
                            <small class="d-block">Back</small>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-3 col-6 mb-3">
                        <div class="enchant-counter missing-chest">
                            <span class="counter-value">0</span>
                            <small class="d-block">Chest</small>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-3 col-6 mb-3">
                        <div class="enchant-counter missing-wrist">
                            <span class="counter-value">0</span>
                            <small class="d-block">Wrist</small>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-3 col-6 mb-3">
                        <div class="enchant-counter missing-legs">
                            <span class="counter-value">0</span>
                            <small class="d-block">Legs</small>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-3 col-6 mb-3">
                        <div class="enchant-counter missing-feet">
                            <span class="counter-value">0</span>
                            <small class="d-block">Feet</small>
                        </div>
                    </div>
                </div>
                <div class="row text-center">
                    <div class="col-lg-4 col-md-4 col-6 mb-3">
                        <div class="enchant-counter missing-finger-1">
                            <span class="counter-value">0</span>
                            <small class="d-block">Ring 1</small>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-4 col-6 mb-3">
                        <div class="enchant-counter missing-finger-2">
                            <span class="counter-value">0</span>
                            <small class="d-block">Ring 2</small>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-4 col-12 mb-3">
                        <div class="enchant-counter missing-main-hand">
                            <span class="counter-value">0</span>
                            <small class="d-block">Weapon</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enchants Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Character Enchants
                </h6>
                <div class="d-flex gap-2">
                    <input type="text" class="form-control form-control-sm search-input"
                           placeholder="Search characters..."
                           data-target=".table-modern"
                           style="width: 200px;">
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-modern mb-0">
                        <thead>
                            <tr>
                                <th data-sort="character-name">
                                    <i class="fas fa-user me-1"></i>Character
                                </th>
                                <th data-sort="head">
                                    <i class="fas fa-hat-wizard me-1"></i>Head
                                </th>
                                <th data-sort="back">
                                    <i class="fas fa-cape me-1"></i>Back
                                </th>
                                <th data-sort="chest">
                                    <i class="fas fa-vest me-1"></i>Chest
                                </th>
                                <th data-sort="wrist">
                                    <i class="fas fa-hand-paper me-1"></i>Wrist
                                </th>
                                <th data-sort="legs">
                                    <i class="fas fa-socks me-1"></i>Legs
                                </th>
                                <th data-sort="feet">
                                    <i class="fas fa-shoe-prints me-1"></i>Feet
                                </th>
                                <th data-sort="finger-1">
                                    <i class="fas fa-ring me-1"></i>Ring 1
                                </th>
                                <th data-sort="finger-2">
                                    <i class="fas fa-ring me-1"></i>Ring 2
                                </th>
                                <th data-sort="main-hand">
                                    <i class="fas fa-sword me-1"></i>Weapon
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for character in characters %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="fw-bold">{{ character.name }}</span>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="enchant-piece {% if character.Head %}has-enchant{% else %}no-enchant{% endif %}">
                                        {% if character.Head %}
                                            <i class="fas fa-check-circle text-success"></i>
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="enchant-piece {% if character.Back %}has-enchant{% else %}no-enchant{% endif %}">
                                        {% if character.Back %}
                                            <i class="fas fa-check-circle text-success"></i>
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="enchant-piece {% if character.Chest %}has-enchant{% else %}no-enchant{% endif %}">
                                        {% if character.Chest %}
                                            <i class="fas fa-check-circle text-success"></i>
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="enchant-piece {% if character.wrist %}has-enchant{% else %}no-enchant{% endif %}">
                                        {% if character.wrist %}
                                            <i class="fas fa-check-circle text-success"></i>
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="enchant-piece {% if character.Legs %}has-enchant{% else %}no-enchant{% endif %}">
                                        {% if character.Legs %}
                                            <i class="fas fa-check-circle text-success"></i>
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="enchant-piece {% if character.feet %}has-enchant{% else %}no-enchant{% endif %}">
                                        {% if character.feet %}
                                            <i class="fas fa-check-circle text-success"></i>
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="enchant-piece {% if character.finger_1 %}has-enchant{% else %}no-enchant{% endif %}">
                                        {% if character.finger_1 %}
                                            <i class="fas fa-check-circle text-success"></i>
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="enchant-piece {% if character.finger_2 %}has-enchant{% else %}no-enchant{% endif %}">
                                        {% if character.finger_2 %}
                                            <i class="fas fa-check-circle text-success"></i>
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="enchant-piece {% if character.Main_hand %}has-enchant{% else %}no-enchant{% endif %}">
                                        {% if character.Main_hand %}
                                            <i class="fas fa-check-circle text-success"></i>
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const table = document.querySelector('.table-modern');
    const headers = table.querySelectorAll('th[data-sort]');
    let currentSort = { column: null, asc: true };

    // Load historical dates
    fetch('/get_historical_enchant_dates')
        .then(response => response.json())
        .then(data => {
            const dateSelect = document.getElementById('dateSelect');
            data.dates.forEach(date => {
                const option = document.createElement('option');
                option.value = date;
                // Format date for display (YYYY-MM-DD)
                const displayDate = `${date.substring(0, 4)}-${date.substring(4, 6)}-${date.substring(6, 8)}`;
                option.textContent = displayDate;
                if (date === '{{ selected_date }}') {
                    option.selected = true;
                }
                dateSelect.appendChild(option);
            });
        })
        .catch(error => console.error('Error loading historical dates:', error));

    // Handle date selection change
    document.getElementById('dateSelect').addEventListener('change', function() {
        const selectedDate = this.value;
        window.location.href = `/enchants?date=${selectedDate}`;
    });

    function updateCounters() {
        const rows = document.querySelectorAll('.table-modern tbody tr:not([style*="display: none"])');
        const totalRaiders = rows.length;

        const enchantCounters = {
            'head': 0,
            'back': 0,
            'chest': 0,
            'wrist': 0,
            'legs': 0,
            'feet': 0,
            'finger-1': 0,
            'finger-2': 0,
            'main-hand': 0
        };

        rows.forEach(row => {
            // Check each enchant slot for missing enchants
            if (row.querySelector('td:nth-child(2) .no-enchant')) enchantCounters['head']++;
            if (row.querySelector('td:nth-child(3) .no-enchant')) enchantCounters['back']++;
            if (row.querySelector('td:nth-child(4) .no-enchant')) enchantCounters['chest']++;
            if (row.querySelector('td:nth-child(5) .no-enchant')) enchantCounters['wrist']++;
            if (row.querySelector('td:nth-child(6) .no-enchant')) enchantCounters['legs']++;
            if (row.querySelector('td:nth-child(7) .no-enchant')) enchantCounters['feet']++;
            if (row.querySelector('td:nth-child(8) .no-enchant')) enchantCounters['finger-1']++;
            if (row.querySelector('td:nth-child(9) .no-enchant')) enchantCounters['finger-2']++;
            if (row.querySelector('td:nth-child(10) .no-enchant')) enchantCounters['main-hand']++;
        });

        // Update enchant counters
        Object.entries(enchantCounters).forEach(([slot, count]) => {
            const counterElement = document.querySelector(`.missing-${slot}`);
            if (counterElement) {
                const counterValue = counterElement.querySelector('.counter-value');
                if (counterValue) {
                    counterValue.textContent = count;
                }

                // Update styling based on count
                if (count === 0) {
                    counterElement.classList.add('zero-count');
                    counterElement.classList.remove('non-zero-count');
                } else {
                    counterElement.classList.add('non-zero-count');
                    counterElement.classList.remove('zero-count');
                }
            }
        });
    }

    // Update counters initially
    updateCounters();

    // Add search functionality
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = table.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });

            // Update counters after filtering
            updateCounters();
        });
    }
});
</script>

<!-- Additional CSS for enchant-specific styling -->
<style>
.enchant-counter {
    padding: 1rem;
    text-align: center;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
}

.enchant-counter .counter-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    display: block;
    margin-bottom: 0.25rem;
}

.enchant-counter small {
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.75rem;
}

/* Border colors based on counter value */
.enchant-counter.zero-count {
    border-color: var(--success-color);
    background: rgba(35, 134, 54, 0.1);
}

.enchant-counter.non-zero-count {
    border-color: var(--danger-color);
    background: rgba(218, 54, 51, 0.1);
}

.enchant-piece {
    display: flex;
    align-items: center;
    justify-content: center;
}

.enchant-piece i {
    font-size: 1.2rem;
}
</style>
{% endblock %}
